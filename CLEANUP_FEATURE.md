# Automatic File Cleanup Feature

## Overview

The Lesson Creator now includes an automatic file cleanup feature that removes downloaded video and audio files after processing is complete. This helps keep your storage clean and prevents accumulation of large media files.

## How It Works

### Automatic Cleanup
- **After Successful Processing**: When a video is successfully downloaded, transcribed, and completed, the system automatically deletes the video (.mp4) and audio (.mp3) files.
- **After Failed Processing**: When processing fails, the system also cleans up any partially downloaded files to prevent storage bloat.
- **Playlist Processing**: Each video in a playlist is cleaned up individually after its transcription is complete.

### Configuration

The cleanup behavior can be controlled via the configuration file at `backend/config/cleanup.js`:

```javascript
module.exports = {
  // Enable automatic cleanup of video and audio files after processing
  enableAutoCleanup: true,
  
  // Cleanup on successful processing completion
  cleanupOnSuccess: true,
  
  // Cleanup on processing failure
  cleanupOnFailure: true,
  
  // Log cleanup operations
  logCleanupOperations: true
};
```

### Manual Cleanup

You can also manually clean up files through:

1. **Web Interface**: Go to the "Processing Status" tab and use the "Clean Up All Completed Videos" button
2. **API Endpoint**: POST to `/api/videos/cleanup` with:
   - `{ "cleanupAll": true }` - Clean up all completed videos
   - `{ "videoId": "VIDEO_ID" }` - Clean up specific video

## What Gets Cleaned Up

- Downloaded video files (`.mp4`) from the `downloaded_videos` folder
- Extracted audio files (`.mp3`) from the `extracted_audios` folder
- Only files associated with videos that have completed processing or failed

## What Stays

- **Database records**: All video metadata, transcriptions, and chapter information remain intact
- **Transcription text**: The actual transcription content is preserved in the database
- **Processing in progress**: Files for videos currently being processed are not touched

## Benefits

1. **Storage Management**: Prevents accumulation of large video/audio files
2. **Clean Workspace**: Keeps download folders organized and ready for new processing
3. **Configurable**: Can be disabled if you want to keep the media files
4. **Safe**: Only removes files after successful transcription or confirmed failure

## Disabling Cleanup

To disable automatic cleanup, edit `backend/config/cleanup.js` and set:

```javascript
module.exports = {
  enableAutoCleanup: false,
  // ... other settings
};
```

## Troubleshooting

- If cleanup fails, it won't affect the transcription process
- Cleanup errors are logged but don't stop video processing
- You can always manually clean up files using the web interface or API
- Check the console logs for detailed cleanup operation information

## File Locations

- **Downloaded Videos**: `downloaded_videos/` folder
- **Extracted Audio**: `extracted_audios/` folder
- **Configuration**: `backend/config/cleanup.js`
- **Cleanup Logic**: `backend/models/Video.js` (cleanupFiles method)
