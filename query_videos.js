const db = require('./backend/database/init');

db.all('SELECT youtube_id, title FROM videos ORDER BY id', (err, rows) => {
  if (err) {
    console.error('Error:', err);
  } else {
    console.log('All videos in database:');
    console.log(JSON.stringify(rows, null, 2));

    // Now look for the specific failing videos
    const failingTitles = [
      '4) Inverse of hyperbola | Part 2',
      '6) Inverse of exponential | Part 1',
      '6) Inverse of exponential | Part 2'
    ];

    console.log('\nLooking for failing videos:');
    failingTitles.forEach(title => {
      const found = rows.find(row => row.title.includes(title));
      if (found) {
        console.log(`Found: ${found.title} -> ${found.youtube_id}`);
      } else {
        console.log(`Not found: ${title}`);
      }
    });
  }
  process.exit(0);
});
