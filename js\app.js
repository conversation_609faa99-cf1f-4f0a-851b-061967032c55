// Lesson Creator Frontend Application
class LessonCreatorApp {
    constructor() {
        this.apiBase = 'http://localhost:3002/api';
        this.currentTab = 'process';
        this.chapters = [];
        this.videos = [];
        this.transcriptions = [];
        this.processingVideos = new Set();
        this.activeJobs = new Map(); // jobId -> job data
        this.websocket = null;

        this.init();
    }

    async init() {
        this.setupEventListeners();
        this.setupTabNavigation();
        this.setupDarkMode();
        this.setupWebSocket();
        await this.loadInitialData();
        this.startStatusPolling();
    }

    setupEventListeners() {
        // Video processing form
        document.getElementById('videoForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.processVideo();
        });

        // Chapter form
        document.getElementById('chapterForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addChapter();
        });

        // Search functionality
        document.getElementById('searchTranscriptions').addEventListener('input', (e) => {
            this.searchTranscriptions(e.target.value);
        });

        // Filter functionality
        document.getElementById('filterChapter').addEventListener('change', (e) => {
            this.filterTranscriptions(e.target.value);
        });

        // Export functionality
        document.getElementById('exportTranscriptions').addEventListener('click', () => {
            this.exportTranscriptions();
        });

        // Modal functionality
        document.getElementById('closeModal').addEventListener('click', () => {
            this.closeModal();
        });

        document.getElementById('closeModalBtn').addEventListener('click', () => {
            this.closeModal();
        });

        document.getElementById('copyTranscription').addEventListener('click', () => {
            this.copyTranscriptionToClipboard();
        });

        // Close modal on backdrop click
        document.getElementById('transcriptionModal').addEventListener('click', (e) => {
            if (e.target.id === 'transcriptionModal') {
                this.closeModal();
            }
        });

        // Cleanup functionality
        document.getElementById('cleanupAllBtn').addEventListener('click', () => {
            this.cleanupAllFiles();
        });
    }

    setupTabNavigation() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabName = button.dataset.tab;
                
                // Update button states
                tabButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                
                // Update content visibility
                tabContents.forEach(content => {
                    content.classList.add('hidden');
                });
                
                document.getElementById(`${tabName}-tab`).classList.remove('hidden');
                this.currentTab = tabName;
                
                // Load tab-specific data
                this.loadTabData(tabName);
            });
        });

        // Tab styling is now handled in CSS
    }

    setupDarkMode() {
        const darkModeToggle = document.getElementById('darkModeToggle');
        const isDark = localStorage.getItem('darkMode') === 'true';
        
        if (isDark) {
            document.documentElement.classList.add('dark');
        }

        darkModeToggle.addEventListener('click', () => {
            document.documentElement.classList.toggle('dark');
            const isDarkMode = document.documentElement.classList.contains('dark');
            localStorage.setItem('darkMode', isDarkMode);
        });
    }

    setupWebSocket() {
        const wsUrl = 'ws://localhost:3002';

        try {
            this.websocket = new WebSocket(wsUrl);

            this.websocket.onopen = () => {
                console.log('WebSocket connected');
            };

            this.websocket.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    if (message.type === 'progress_update') {
                        this.handleProgressUpdate(message.data);
                    }
                } catch (error) {
                    console.error('Error parsing WebSocket message:', error);
                }
            };

            this.websocket.onclose = () => {
                console.log('WebSocket disconnected');
                // Attempt to reconnect after 5 seconds
                setTimeout(() => {
                    this.setupWebSocket();
                }, 5000);
            };

            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
            };

        } catch (error) {
            console.error('Failed to setup WebSocket:', error);
        }
    }

    handleProgressUpdate(jobData) {
        this.activeJobs.set(jobData.id, jobData);
        this.updateProgressDisplay(jobData);

        // Update UI based on job status
        if (jobData.status === 'completed' || jobData.status === 'completed_with_errors') {
            this.showToast(
                `${jobData.type === 'playlist' ? 'Playlist' : 'Video'} processing completed!`,
                jobData.status === 'completed' ? 'success' : 'warning'
            );

            // Refresh data
            this.loadVideos();
            this.loadTranscriptions();
            this.loadRecentVideos();
        } else if (jobData.status === 'failed') {
            this.showToast(
                `${jobData.type === 'playlist' ? 'Playlist' : 'Video'} processing failed`,
                'error'
            );
        }
    }

    updateProgressDisplay(jobData) {
        // Find or create progress container
        let progressContainer = document.getElementById(`progress-${jobData.id}`);

        if (!progressContainer && (jobData.status === 'started' || jobData.status === 'processing')) {
            progressContainer = this.createProgressContainer(jobData);
        }

        if (progressContainer) {
            this.updateProgressContainer(progressContainer, jobData);

            // Remove completed jobs after a delay
            if (jobData.status === 'completed' || jobData.status === 'failed' || jobData.status === 'completed_with_errors') {
                setTimeout(() => {
                    if (progressContainer.parentNode) {
                        progressContainer.parentNode.removeChild(progressContainer);
                    }
                }, 5000);
            }
        }
    }

    createProgressContainer(jobData) {
        const container = document.createElement('div');
        container.id = `progress-${jobData.id}`;
        container.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-4 border border-gray-200 dark:border-gray-700';

        // Add to the processing status area
        const statusContainer = document.querySelector('#status .space-y-6') || document.querySelector('#status');
        if (statusContainer) {
            statusContainer.appendChild(container);
        }

        return container;
    }

    updateProgressContainer(container, jobData) {
        const isPlaylist = jobData.type === 'playlist';
        const title = isPlaylist ? jobData.data?.playlistTitle || 'Playlist' : jobData.data?.title || 'Video';

        container.innerHTML = `
            <div class="flex items-center justify-between mb-2">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    ${isPlaylist ? '📋' : '🎥'} ${title}
                </h3>
                <span class="text-sm text-gray-500 dark:text-gray-400">
                    ${jobData.status.replace('_', ' ').toUpperCase()}
                </span>
            </div>

            ${isPlaylist ? `
                <div class="mb-2">
                    <div class="flex justify-between text-sm text-gray-600 dark:text-gray-300">
                        <span>Overall Progress</span>
                        <span>${jobData.progress || 0}%</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                             style="width: ${jobData.progress || 0}%"></div>
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        ${jobData.currentStep || 0} / ${jobData.totalSteps || 0} videos processed
                    </div>
                </div>

                ${jobData.videos && jobData.videos.length > 0 ? `
                    <div class="space-y-2 max-h-40 overflow-y-auto">
                        ${jobData.videos.map(video => `
                            <div class="flex items-center justify-between text-sm p-2 bg-gray-50 dark:bg-gray-700 rounded">
                                <span class="truncate flex-1">${video.title || video.id}</span>
                                <div class="flex items-center space-x-2">
                                    <span class="text-xs ${this.getStatusColor(video.status)}">${video.status || 'pending'}</span>
                                    <span class="text-xs text-gray-500">${video.progress || 0}%</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                ` : ''}
            ` : `
                <div class="mb-2">
                    <div class="flex justify-between text-sm text-gray-600 dark:text-gray-300">
                        <span>Progress</span>
                        <span>${jobData.progress || 0}%</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                             style="width: ${jobData.progress || 0}%"></div>
                    </div>
                </div>
            `}
        `;
    }

    getStatusColor(status) {
        switch (status) {
            case 'completed': return 'text-green-600';
            case 'failed': return 'text-red-600';
            case 'downloading': return 'text-blue-600';
            case 'extracting_audio': return 'text-yellow-600';
            default: return 'text-gray-600';
        }
    }

    async loadInitialData() {
        try {
            await Promise.all([
                this.loadChapters(),
                this.loadVideos(),
                this.loadTranscriptions()
            ]);
            this.updateStatistics();
        } catch (error) {
            this.showToast('Failed to load initial data', 'error');
            console.error('Error loading initial data:', error);
        }
    }

    async loadTabData(tabName) {
        switch (tabName) {
            case 'process':
                await this.loadRecentVideos();
                break;
            case 'chapters':
                await this.loadChapters();
                this.renderChaptersList();
                break;
            case 'transcriptions':
                await this.loadTranscriptions();
                this.renderTranscriptionsList();
                break;
            case 'status':
                await this.loadProcessingStatus();
                this.updateStatistics();
                break;
        }
    }

    async apiRequest(endpoint, options = {}) {
        try {
            const response = await fetch(`${this.apiBase}${endpoint}`, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });

            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || data.error || 'Request failed');
            }

            return data;
        } catch (error) {
            console.error(`API request failed: ${endpoint}`, error);
            throw error;
        }
    }

    async loadChapters() {
        try {
            const response = await this.apiRequest('/chapters');
            this.chapters = response.data || [];
            this.updateChapterSelects();
        } catch (error) {
            console.error('Error loading chapters:', error);
        }
    }

    async loadVideos() {
        try {
            const response = await this.apiRequest('/videos');
            this.videos = response.data || [];
        } catch (error) {
            console.error('Error loading videos:', error);
        }
    }

    async loadTranscriptions() {
        try {
            const response = await this.apiRequest('/transcriptions?with_details=true');
            this.transcriptions = response.data || [];
        } catch (error) {
            console.error('Error loading transcriptions:', error);
        }
    }

    updateChapterSelects() {
        const selects = ['chapterSelect', 'filterChapter'];
        
        selects.forEach(selectId => {
            const select = document.getElementById(selectId);
            if (!select) return;
            
            // Clear existing options (except first one)
            while (select.children.length > 1) {
                select.removeChild(select.lastChild);
            }
            
            // Add chapter options
            this.chapters.forEach(chapter => {
                const option = document.createElement('option');
                option.value = chapter.id;
                option.textContent = chapter.name;
                select.appendChild(option);
            });
        });
    }

    async processVideo() {
        const form = document.getElementById('videoForm');
        const formData = new FormData(form);
        const url = formData.get('url');
        const chapterId = formData.get('chapter_id') || null;

        if (!url) {
            this.showToast('Please enter a YouTube URL', 'error');
            return;
        }

        const processButton = document.getElementById('processButton');
        const processButtonText = document.getElementById('processButtonText');
        const processSpinner = document.getElementById('processSpinner');

        try {
            // Update button state
            processButton.disabled = true;
            processButtonText.textContent = 'Processing...';
            processSpinner.classList.remove('hidden');

            const response = await this.apiRequest('/videos/process', {
                method: 'POST',
                body: JSON.stringify({ url, chapter_id: chapterId })
            });

            // Handle different response types
            if (response.type === 'playlist') {
                this.showToast(`Playlist processing started! Processing multiple videos in background.`, 'success');
                // Store job ID for tracking
                if (response.jobId) {
                    this.activeJobs.set(response.jobId, {
                        id: response.jobId,
                        type: 'playlist',
                        status: 'started'
                    });
                }
            } else {
                this.showToast('Video processing started successfully', 'success');
                // Add to processing set for single videos
                this.processingVideos.add(response.videoId);
                // Store job ID for tracking
                if (response.jobId) {
                    this.activeJobs.set(response.jobId, {
                        id: response.jobId,
                        type: 'video',
                        status: 'started'
                    });
                }
            }

            // Clear form
            form.reset();

            // Refresh data
            await this.loadVideos();
            await this.loadRecentVideos();

        } catch (error) {
            this.showToast(error.message || 'Failed to process video', 'error');
        } finally {
            // Reset button state
            processButton.disabled = false;
            processButtonText.textContent = 'Process Video';
            processSpinner.classList.add('hidden');
        }
    }

    async addChapter() {
        const form = document.getElementById('chapterForm');
        const formData = new FormData(form);
        const name = formData.get('name');
        const description = formData.get('description');

        if (!name) {
            this.showToast('Please enter a chapter name', 'error');
            return;
        }

        try {
            await this.apiRequest('/chapters', {
                method: 'POST',
                body: JSON.stringify({ name, description })
            });

            this.showToast('Chapter added successfully', 'success');
            
            // Clear form
            form.reset();
            
            // Refresh data
            await this.loadChapters();
            this.renderChaptersList();

        } catch (error) {
            this.showToast(error.message || 'Failed to add chapter', 'error');
        }
    }

    async loadRecentVideos() {
        try {
            const container = document.getElementById('recentVideos');

            if (this.videos.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted-foreground py-8">
                        No videos processed yet
                    </div>
                `;
                return;
            }

            // Show last 5 videos
            const recentVideos = this.videos.slice(0, 5);

            container.innerHTML = recentVideos.map(video => `
                <div class="border border-border rounded-lg p-4 fade-in">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h4 class="font-medium text-sm">${this.escapeHtml(video.title)}</h4>
                            <p class="text-xs text-muted-foreground mt-1">
                                ${video.youtube_id} • ${this.formatDate(video.created_at)}
                            </p>
                        </div>
                        <div class="flex items-center gap-2">
                            <span class="status-badge status-${video.status} px-2 py-1 text-xs rounded-full border">
                                ${this.capitalizeFirst(video.status)}
                            </span>
                            <button
                                onclick="app.deleteVideo(${video.id}, '${this.escapeHtml(video.title).replace(/'/g, "\\'")}')"
                                class="text-xs bg-red-500 text-white px-2 py-1 rounded hover:bg-red-600 transition-colors"
                                title="Delete video and associated files"
                            >
                                Delete
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');

        } catch (error) {
            console.error('Error loading recent videos:', error);
        }
    }

    renderChaptersList() {
        const container = document.getElementById('chaptersList');

        if (this.chapters.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted-foreground py-8">
                    No chapters created yet
                </div>
            `;
            return;
        }

        container.innerHTML = this.chapters.map(chapter => `
            <div class="border border-border rounded-lg p-4 fade-in">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <h4 class="font-medium">${this.escapeHtml(chapter.name)}</h4>
                        ${chapter.description ? `<p class="text-sm text-muted-foreground mt-1">${this.escapeHtml(chapter.description)}</p>` : ''}
                        <p class="text-xs text-muted-foreground mt-2">
                            Created: ${this.formatDate(chapter.created_at)}
                        </p>
                    </div>
                    <div class="flex gap-2">
                        <button
                            onclick="app.viewChapterTranscriptions(${chapter.id})"
                            class="text-xs bg-secondary text-secondary-foreground px-3 py-1 rounded hover:bg-secondary/90 transition-colors"
                        >
                            View Transcriptions
                        </button>
                        <button
                            onclick="app.deleteChapter(${chapter.id})"
                            class="text-xs bg-red-100 text-red-800 px-3 py-1 rounded hover:bg-red-200 transition-colors"
                        >
                            Delete
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    renderTranscriptionsList(transcriptions = null) {
        const container = document.getElementById('transcriptionsList');
        const items = transcriptions || this.transcriptions;

        if (items.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted-foreground py-8">
                    No transcriptions available
                </div>
            `;
            return;
        }

        container.innerHTML = items.map(transcription => `
            <div class="border border-border rounded-lg p-4 fade-in">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <h4 class="font-medium">${this.escapeHtml(transcription.title)}</h4>
                        <p class="text-sm text-muted-foreground mt-1">
                            Video: ${this.escapeHtml(transcription.video_title || 'Unknown')}
                        </p>
                        ${transcription.chapter_name ? `
                            <p class="text-xs text-muted-foreground mt-1">
                                Chapter: ${this.escapeHtml(transcription.chapter_name)}
                            </p>
                        ` : ''}
                        <p class="text-xs text-muted-foreground mt-2">
                            Created: ${this.formatDate(transcription.created_at)}
                        </p>
                        <p class="text-sm mt-3 line-clamp-3">
                            ${this.escapeHtml(transcription.content.substring(0, 200))}${transcription.content.length > 200 ? '...' : ''}
                        </p>
                        ${transcription.chapter_index ? `
                            <p class="text-xs text-muted-foreground mt-1 font-medium">
                                #${transcription.chapter_index}
                            </p>
                        ` : ''}
                    </div>
                    <div class="flex flex-col gap-2 ml-4">
                        <button
                            onclick="app.viewTranscription(${transcription.id})"
                            class="text-xs bg-primary text-primary-foreground px-3 py-1 rounded hover:bg-primary/90 transition-colors"
                        >
                            View Full
                        </button>
                        <button
                            onclick="app.copyTranscriptionText('${transcription.content.replace(/'/g, "\\'")}')"
                            class="text-xs bg-secondary text-secondary-foreground px-3 py-1 rounded hover:bg-secondary/90 transition-colors"
                        >
                            Copy
                        </button>
                        <button
                            onclick="app.deleteTranscription(${transcription.id}, '${this.escapeHtml(transcription.title).replace(/'/g, "\\'")}')"
                            class="text-xs bg-red-500 text-white px-3 py-1 rounded hover:bg-red-600 transition-colors"
                        >
                            Delete
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    async loadProcessingStatus() {
        try {
            const container = document.getElementById('processingStatus');
            const processingVideos = this.videos.filter(video =>
                ['pending', 'downloading', 'processing', 'transcribing'].includes(video.status)
            );

            if (processingVideos.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted-foreground py-8">
                        No videos in processing queue
                    </div>
                `;
                return;
            }

            container.innerHTML = processingVideos.map(video => `
                <div class="border border-border rounded-lg p-4 fade-in">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h4 class="font-medium">${this.escapeHtml(video.title)}</h4>
                            <p class="text-sm text-muted-foreground mt-1">
                                ${video.youtube_id} • Started: ${this.formatDate(video.created_at)}
                            </p>
                        </div>
                        <div class="flex items-center gap-3">
                            <span class="status-badge status-${video.status} px-3 py-1 text-sm rounded-full border">
                                ${this.capitalizeFirst(video.status)}
                            </span>
                            ${video.status !== 'failed' ? '<div class="spinner"></div>' : ''}
                        </div>
                    </div>
                </div>
            `).join('');

        } catch (error) {
            console.error('Error loading processing status:', error);
        }
    }

    updateStatistics() {
        document.getElementById('totalVideos').textContent = this.videos.length;
        document.getElementById('totalTranscriptions').textContent = this.transcriptions.length;
        document.getElementById('totalChapters').textContent = this.chapters.length;
    }

    searchTranscriptions(query) {
        if (!query.trim()) {
            this.renderTranscriptionsList();
            return;
        }

        const filtered = this.transcriptions.filter(transcription =>
            transcription.title.toLowerCase().includes(query.toLowerCase()) ||
            transcription.content.toLowerCase().includes(query.toLowerCase()) ||
            (transcription.video_title && transcription.video_title.toLowerCase().includes(query.toLowerCase()))
        );

        this.renderTranscriptionsList(filtered);
    }

    filterTranscriptions(chapterId) {
        if (!chapterId) {
            this.renderTranscriptionsList();
            return;
        }

        const filtered = this.transcriptions.filter(transcription =>
            transcription.chapter_id == chapterId
        );

        this.renderTranscriptionsList(filtered);
    }

    async viewTranscription(transcriptionId) {
        try {
            const response = await this.apiRequest(`/transcriptions/${transcriptionId}`);
            const transcription = response.data;

            this.showTranscriptionModal(transcription);
        } catch (error) {
            this.showToast('Failed to load transcription details', 'error');
        }
    }

    showTranscriptionModal(transcription) {
        const modal = document.getElementById('transcriptionModal');
        const title = document.getElementById('modalTitle');
        const content = document.getElementById('modalContent');

        title.textContent = transcription.title;
        content.innerHTML = `
            <div class="space-y-4">
                <div>
                    <h4 class="font-medium text-sm text-muted-foreground">Video Title</h4>
                    <p class="mt-1">${this.escapeHtml(transcription.video_title || 'Unknown')}</p>
                </div>
                <div>
                    <h4 class="font-medium text-sm text-muted-foreground">Language</h4>
                    <p class="mt-1">${transcription.language || 'en'}</p>
                </div>
                <div>
                    <h4 class="font-medium text-sm text-muted-foreground">Created</h4>
                    <p class="mt-1">${this.formatDate(transcription.created_at)}</p>
                </div>
                <div>
                    <h4 class="font-medium text-sm text-muted-foreground">Transcription Content</h4>
                    <div class="mt-2 p-4 bg-muted rounded-lg max-h-96 overflow-y-auto">
                        <p class="whitespace-pre-wrap">${this.escapeHtml(transcription.content)}</p>
                    </div>
                </div>
            </div>
        `;

        // Store current transcription for copying
        this.currentTranscription = transcription;

        modal.classList.remove('hidden');
        modal.classList.add('flex');
    }

    closeModal() {
        const modal = document.getElementById('transcriptionModal');
        modal.classList.add('hidden');
        modal.classList.remove('flex');
        this.currentTranscription = null;
    }

    async copyTranscriptionToClipboard() {
        if (!this.currentTranscription) return;

        try {
            await navigator.clipboard.writeText(this.currentTranscription.content);
            this.showToast('Transcription copied to clipboard', 'success');
        } catch (error) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = this.currentTranscription.content;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showToast('Transcription copied to clipboard', 'success');
        }
    }

    async copyTranscriptionText(content) {
        try {
            await navigator.clipboard.writeText(content);
            this.showToast('Transcription copied to clipboard', 'success');
        } catch (error) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = content;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showToast('Transcription copied to clipboard', 'success');
        }
    }

    async deleteTranscription(transcriptionId, title) {
        if (!confirm(`Are you sure you want to delete the transcription "${title}"?\n\nThis action cannot be undone.`)) {
            return;
        }

        try {
            const response = await this.apiRequest(`/transcriptions/${transcriptionId}`, {
                method: 'DELETE'
            });

            if (response.success) {
                this.showToast('Transcription deleted successfully!', 'success');

                // Refresh transcriptions list
                await this.loadTranscriptions();
                this.renderTranscriptionsList();

                // Update statistics
                this.updateStatistics();
            } else {
                throw new Error(response.error || 'Failed to delete transcription');
            }
        } catch (error) {
            console.error('Error deleting transcription:', error);
            this.showToast(error.message || 'Failed to delete transcription', 'error');
        }
    }

    async deleteVideo(videoId, title) {
        if (!confirm(`Are you sure you want to delete the video "${title}"?\n\nThis will also delete:\n- Downloaded video file\n- Extracted audio file\n- All associated transcriptions\n\nThis action cannot be undone.`)) {
            return;
        }

        try {
            const response = await this.apiRequest(`/videos/${videoId}`, {
                method: 'DELETE'
            });

            if (response.success) {
                this.showToast('Video and associated files deleted successfully!', 'success');

                // Refresh all data
                await this.loadVideos();
                await this.loadTranscriptions();
                await this.loadRecentVideos();
                this.renderTranscriptionsList();

                // Update statistics
                this.updateStatistics();
            } else {
                throw new Error(response.error || 'Failed to delete video');
            }
        } catch (error) {
            console.error('Error deleting video:', error);
            this.showToast(error.message || 'Failed to delete video', 'error');
        }
    }

    async viewChapterTranscriptions(chapterId) {
        // Switch to transcriptions tab
        document.querySelector('[data-tab="transcriptions"]').click();

        // Set filter
        document.getElementById('filterChapter').value = chapterId;
        this.filterTranscriptions(chapterId);
    }

    async deleteChapter(chapterId) {
        if (!confirm('Are you sure you want to delete this chapter? This action cannot be undone.')) {
            return;
        }

        try {
            await this.apiRequest(`/chapters/${chapterId}`, {
                method: 'DELETE'
            });

            this.showToast('Chapter deleted successfully', 'success');

            // Refresh data
            await this.loadChapters();
            this.renderChaptersList();

        } catch (error) {
            this.showToast(error.message || 'Failed to delete chapter', 'error');
        }
    }

    async exportTranscriptions() {
        const chapterId = document.getElementById('filterChapter').value;

        try {
            let url = '/transcriptions/export';
            if (chapterId) {
                url = `/transcriptions/export/chapter/${chapterId}`;
            }

            const response = await fetch(`${this.apiBase}${url}?format=json`);
            const blob = await response.blob();

            // Create download link
            const downloadUrl = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = chapterId ? `chapter_${chapterId}_transcriptions.json` : 'all_transcriptions.json';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(downloadUrl);

            this.showToast('Transcriptions exported successfully', 'success');
        } catch (error) {
            this.showToast('Failed to export transcriptions', 'error');
        }
    }

    startStatusPolling() {
        // Poll for status updates every 5 seconds
        setInterval(async () => {
            if (this.processingVideos.size > 0) {
                await this.loadVideos();

                // Update processing videos set
                const stillProcessing = this.videos.filter(video =>
                    ['pending', 'downloading', 'processing', 'transcribing'].includes(video.status)
                ).map(video => video.id);

                this.processingVideos = new Set(stillProcessing);

                // Update current tab if it's status
                if (this.currentTab === 'status') {
                    await this.loadProcessingStatus();
                }

                // Update recent videos if on process tab
                if (this.currentTab === 'process') {
                    await this.loadRecentVideos();
                }

                // Update statistics
                this.updateStatistics();
            }
        }, 5000);
    }

    async cleanupAllFiles() {
        const cleanupBtn = document.getElementById('cleanupAllBtn');
        const cleanupText = document.getElementById('cleanupAllText');
        const cleanupSpinner = document.getElementById('cleanupAllSpinner');
        const cleanupResult = document.getElementById('cleanupResult');

        // Show loading state
        cleanupBtn.disabled = true;
        cleanupText.textContent = 'Cleaning up...';
        cleanupSpinner.classList.remove('hidden');
        cleanupResult.classList.add('hidden');

        try {
            const response = await this.apiRequest('/videos/cleanup', {
                method: 'POST',
                body: JSON.stringify({ cleanupAll: true })
            });

            if (response.success) {
                cleanupResult.className = 'p-3 rounded-lg bg-green-100 border border-green-200 text-green-800';
                cleanupResult.innerHTML = `
                    <div class="font-medium">Cleanup Completed Successfully</div>
                    <div class="text-sm mt-1">
                        ${response.data.cleaned} videos cleaned,
                        ${response.data.errors} errors,
                        ${response.data.total} total videos processed
                    </div>
                `;
                this.showToast('File cleanup completed successfully', 'success');
            } else {
                throw new Error(response.error || 'Cleanup failed');
            }

        } catch (error) {
            console.error('Cleanup failed:', error);
            cleanupResult.className = 'p-3 rounded-lg bg-red-100 border border-red-200 text-red-800';
            cleanupResult.innerHTML = `
                <div class="font-medium">Cleanup Failed</div>
                <div class="text-sm mt-1">${error.message}</div>
            `;
            this.showToast('File cleanup failed: ' + error.message, 'error');

        } finally {
            // Reset button state
            cleanupBtn.disabled = false;
            cleanupText.textContent = 'Clean Up All Completed Videos';
            cleanupSpinner.classList.add('hidden');
            cleanupResult.classList.remove('hidden');
        }
    }

    showToast(message, type = 'info') {
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');

        const bgColor = {
            success: 'bg-green-100 border-green-200 text-green-800',
            error: 'bg-red-100 border-red-200 text-red-800',
            warning: 'bg-yellow-100 border-yellow-200 text-yellow-800',
            info: 'bg-blue-100 border-blue-200 text-blue-800'
        }[type] || 'bg-blue-100 border-blue-200 text-blue-800';

        toast.className = `${bgColor} border rounded-lg p-4 shadow-lg fade-in max-w-sm`;
        toast.innerHTML = `
            <div class="flex items-center justify-between">
                <span class="text-sm font-medium">${this.escapeHtml(message)}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-3 text-current opacity-70 hover:opacity-100">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        `;

        container.appendChild(toast);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 5000);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    capitalizeFirst(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new LessonCreatorApp();
});
