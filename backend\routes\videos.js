const express = require('express');
const router = express.Router();
const Video = require('../models/Video');
const youtubeService = require('../services/youtubeService');
const TranscriptionService = require('../services/transcriptionService');
const progressService = require('../services/progressService');
const cleanupConfig = require('../config/cleanup');

// Initialize transcription service
const transcriptionService = new TranscriptionService();

// Get all videos
router.get('/', async (req, res) => {
  try {
    const { status, chapter_id } = req.query;
    let videos;

    if (status) {
      videos = await Video.findByStatus(status);
    } else if (chapter_id) {
      videos = await Video.findByChapterId(chapter_id);
    } else {
      videos = await Video.findAll();
    }

    res.json({
      success: true,
      data: videos.map(video => video.toJSON())
    });
  } catch (error) {
    console.error('Error fetching videos:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch videos',
      message: error.message
    });
  }
});

// Get video by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const video = await Video.findById(id);
    
    if (!video) {
      return res.status(404).json({
        success: false,
        error: 'Video not found'
      });
    }

    res.json({
      success: true,
      data: video.toJSON()
    });
  } catch (error) {
    console.error('Error fetching video:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch video',
      message: error.message
    });
  }
});

// Process YouTube video or playlist (download + extract audio + transcribe)
router.post('/process', async (req, res) => {
  try {
    const { url, chapter_id } = req.body;

    if (!url) {
      return res.status(400).json({
        success: false,
        error: 'YouTube URL is required'
      });
    }

    // Check if it's a playlist
    if (youtubeService.isPlaylistUrl(url)) {
      // Process playlist
      const playlistId = youtubeService.extractPlaylistId(url);
      if (!playlistId) {
        return res.status(400).json({
          success: false,
          error: 'Invalid YouTube playlist URL'
        });
      }

      // Create progress tracking job
      const jobId = progressService.generateJobId();
      progressService.createJob(jobId, 'playlist', {
        playlistId: playlistId,
        url: url,
        chapterId: chapter_id
      });

      // Start playlist processing in background
      processPlaylistAsync(url, chapter_id, playlistId, jobId);

      res.status(202).json({
        success: true,
        message: 'Playlist processing started',
        playlistId: playlistId,
        jobId: jobId,
        type: 'playlist'
      });

    } else {
      // Process single video
      const videoId = youtubeService.extractVideoId(url);
      if (!videoId) {
        return res.status(400).json({
          success: false,
          error: 'Invalid YouTube URL'
        });
      }

      // Check if video already exists
      let video = await Video.findByYouTubeId(videoId);
      if (video) {
        return res.status(409).json({
          success: false,
          error: 'Video already exists in the system',
          data: video.toJSON()
        });
      }

      // Create progress tracking job
      const jobId = progressService.generateJobId();
      progressService.createJob(jobId, 'video', {
        videoId: videoId,
        url: url,
        chapterId: chapter_id
      });

      // Start processing in background
      processVideoAsync(url, chapter_id, videoId, jobId);

      res.status(202).json({
        success: true,
        message: 'Video processing started',
        videoId: videoId,
        jobId: jobId,
        type: 'video'
      });
    }

  } catch (error) {
    console.error('Error starting video/playlist processing:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to start video/playlist processing',
      message: error.message
    });
  }
});

// Async function to process video in background
async function processVideoAsync(url, chapterId, videoId, jobId = null) {
  try {
    console.log(`Starting background processing for video: ${videoId}`);

    // Get video info first
    const videoInfo = await youtubeService.getVideoInfo(videoId);

    // Create video record
    const video = await Video.create({
      youtube_id: videoId,
      title: videoInfo.title,
      description: videoInfo.description,
      duration: videoInfo.duration,
      chapter_id: chapterId,
      status: 'downloading'
    });

    // Process video (download + extract audio)
    const processResult = await youtubeService.processVideo(url, chapterId, jobId);

    // Update video with file paths
    await video.update({
      video_path: processResult.videoPath,
      audio_path: processResult.audioPath,
      status: 'processing'
    });

    // Transcribe audio
    const transcription = await transcriptionService.processVideoTranscription(
      videoId,
      processResult.audioPath,
      chapterId
    );

    // Complete the job if tracking
    if (jobId) {
      progressService.completeJob(jobId, 'completed', {
        videoId: videoId,
        title: videoInfo.title
      });
    }

    // Clean up video and audio files after successful processing
    if (cleanupConfig.enableAutoCleanup && cleanupConfig.cleanupOnSuccess) {
      try {
        await video.cleanupFiles();
        if (cleanupConfig.logCleanupOperations) {
          console.log(`Cleaned up files for video: ${videoId}`);
        }
      } catch (cleanupError) {
        console.error(`Failed to cleanup files for video ${videoId}:`, cleanupError.message);
        // Don't fail the entire process if cleanup fails
      }
    }

    console.log(`Video processing completed successfully: ${videoId}`);

  } catch (error) {
    console.error(`Video processing failed for ${videoId}:`, error);

    // Complete the job with error if tracking
    if (jobId) {
      progressService.completeJob(jobId, 'failed', {
        error: error.message,
        videoId: videoId
      });
    }

    // Update video status to failed and cleanup files
    try {
      const video = await Video.findByYouTubeId(videoId);
      if (video) {
        await video.update({ status: 'failed' });

        // Clean up any downloaded files even if processing failed
        if (cleanupConfig.enableAutoCleanup && cleanupConfig.cleanupOnFailure) {
          try {
            await video.cleanupFiles();
            if (cleanupConfig.logCleanupOperations) {
              console.log(`Cleaned up files for failed video: ${videoId}`);
            }
          } catch (cleanupError) {
            console.error(`Failed to cleanup files for failed video ${videoId}:`, cleanupError.message);
          }
        }
      }
    } catch (updateError) {
      console.error('Failed to update video status:', updateError);
    }
  }
}

// Async function to process playlist in background
async function processPlaylistAsync(url, chapterId, playlistId, jobId = null) {
  try {
    console.log(`Starting background processing for playlist: ${playlistId}`);

    // Process the playlist (download + extract audio)
    const result = await youtubeService.processPlaylist(url, chapterId, jobId);

    console.log(`Playlist processing completed: ${result.summary.successful}/${result.summary.total} videos processed successfully`);

    // Now process transcriptions for successful videos
    if (result.results.length > 0) {
      console.log(`Starting transcription for ${result.results.length} videos...`);

      // Videos are already processed sequentially, so results are in correct order
      for (const videoResult of result.results) {
        try {
          console.log(`Processing transcription ${videoResult.playlistIndex}: ${videoResult.info.title}`);

          // Check if video already exists in database
          let video = await Video.findByYouTubeId(videoResult.videoId);

          if (video) {
            console.log(`Video already exists in database: ${videoResult.info.title}`);

            // Update existing video with new file paths if they're different
            if (video.video_path !== videoResult.videoPath || video.audio_path !== videoResult.audioPath) {
              await video.update({
                video_path: videoResult.videoPath,
                audio_path: videoResult.audioPath,
                status: 'processing'
              });
            }
          } else {
            // Create new video record in database
            video = await Video.create({
              youtube_id: videoResult.videoId,
              title: videoResult.info.title,
              description: videoResult.info.description,
              duration: videoResult.info.duration,
              chapter_id: chapterId,
              video_path: videoResult.videoPath,
              audio_path: videoResult.audioPath,
              status: 'processing'
            });
          }

          // Check if transcription already exists
          const existingTranscription = await video.getTranscription();

          if (existingTranscription) {
            console.log(`Transcription already exists for video: ${videoResult.info.title}`);

            // Update video status to completed if not already
            if (video.status !== 'completed') {
              await video.update({ status: 'completed' });
            }

            // Update progress: already completed
            if (jobId) {
              progressService.updateVideoProgress(jobId, videoResult.videoId, {
                title: videoResult.info.title,
                status: 'completed',
                progress: 100,
                phase: 'completed'
              });
            }
          } else {
            // Update progress: starting transcription
            if (jobId) {
              progressService.updateVideoProgress(jobId, videoResult.videoId, {
                title: videoResult.info.title,
                status: 'transcribing',
                progress: 75,
                phase: 'transcription'
              });
            }

            // Generate transcription
            const transcription = await transcriptionService.processVideoTranscription(
              videoResult.videoId,
              videoResult.audioPath,
              chapterId,
              videoResult.playlistIndex
            );

            // Update video status to completed
            await video.update({ status: 'completed' });

            // Clean up video and audio files after successful processing
            if (cleanupConfig.enableAutoCleanup && cleanupConfig.cleanupOnSuccess) {
              try {
                await video.cleanupFiles();
                if (cleanupConfig.logCleanupOperations) {
                  console.log(`Cleaned up files for video: ${videoResult.videoId}`);
                }
              } catch (cleanupError) {
                console.error(`Failed to cleanup files for video ${videoResult.videoId}:`, cleanupError.message);
                // Don't fail the entire process if cleanup fails
              }
            }
          }

          // Update progress: completed
          if (jobId) {
            progressService.updateVideoProgress(jobId, videoResult.videoId, {
              title: videoResult.info.title,
              status: 'completed',
              progress: 100,
              phase: 'completed'
            });
          }

          console.log(`Transcription completed for video: ${videoResult.info.title}`);

        } catch (transcriptionError) {
          console.error(`Transcription failed for video ${videoResult.info.title}:`, transcriptionError);

          // Clean up files for failed transcription
          if (cleanupConfig.enableAutoCleanup && cleanupConfig.cleanupOnFailure) {
            try {
              const video = await Video.findByYouTubeId(videoResult.videoId);
              if (video) {
                await video.cleanupFiles();
                if (cleanupConfig.logCleanupOperations) {
                  console.log(`Cleaned up files for failed transcription: ${videoResult.videoId}`);
                }
              }
            } catch (cleanupError) {
              console.error(`Failed to cleanup files for failed transcription ${videoResult.videoId}:`, cleanupError.message);
            }
          }

          // Update progress: failed transcription
          if (jobId) {
            progressService.updateVideoProgress(jobId, videoResult.videoId, {
              title: videoResult.info.title,
              status: 'failed',
              progress: 75,
              phase: 'transcription',
              error: transcriptionError.message
            });
          }
        }
      }
    }

    if (result.errors.length > 0) {
      console.log('Some videos failed to process:', result.errors);
    }

    console.log(`Playlist processing fully completed: ${result.summary.successful} videos with transcriptions`);

  } catch (error) {
    console.error(`Playlist processing failed for ${playlistId}:`, error);

    // Complete the job with error if tracking
    if (jobId) {
      progressService.completeJob(jobId, 'failed', {
        error: error.message,
        playlistId: playlistId
      });
    }
  }
}

// Get job progress
router.get('/progress/:jobId', async (req, res) => {
  try {
    const { jobId } = req.params;
    const job = progressService.getJob(jobId);

    if (!job) {
      return res.status(404).json({
        success: false,
        error: 'Job not found'
      });
    }

    res.json({
      success: true,
      data: job
    });
  } catch (error) {
    console.error('Error fetching job progress:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch job progress',
      message: error.message
    });
  }
});

// Get all active jobs
router.get('/progress', async (req, res) => {
  try {
    const jobs = progressService.getAllJobs();
    res.json({
      success: true,
      data: jobs
    });
  } catch (error) {
    console.error('Error fetching jobs:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch jobs',
      message: error.message
    });
  }
});

// Get video processing status
router.get('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const video = await Video.findById(id);
    
    if (!video) {
      return res.status(404).json({
        success: false,
        error: 'Video not found'
      });
    }

    res.json({
      success: true,
      data: {
        id: video.id,
        youtube_id: video.youtube_id,
        title: video.title,
        status: video.status,
        created_at: video.created_at,
        updated_at: video.updated_at
      }
    });
  } catch (error) {
    console.error('Error fetching video status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch video status',
      message: error.message
    });
  }
});

// Get video transcription
router.get('/:id/transcription', async (req, res) => {
  try {
    const { id } = req.params;
    const video = await Video.findById(id);
    
    if (!video) {
      return res.status(404).json({
        success: false,
        error: 'Video not found'
      });
    }

    const transcription = await video.getTranscription();
    
    if (!transcription) {
      return res.status(404).json({
        success: false,
        error: 'Transcription not found for this video'
      });
    }

    res.json({
      success: true,
      data: transcription.toJSON()
    });
  } catch (error) {
    console.error('Error fetching video transcription:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch video transcription',
      message: error.message
    });
  }
});

// Update video
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const video = await Video.findById(id);
    if (!video) {
      return res.status(404).json({
        success: false,
        error: 'Video not found'
      });
    }

    const updatedVideo = await video.update(updateData);
    
    res.json({
      success: true,
      data: updatedVideo.toJSON(),
      message: 'Video updated successfully'
    });
  } catch (error) {
    console.error('Error updating video:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update video',
      message: error.message
    });
  }
});

// Delete video
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const video = await Video.findById(id);
    if (!video) {
      return res.status(404).json({
        success: false,
        error: 'Video not found'
      });
    }

    await video.delete();
    
    res.json({
      success: true,
      message: 'Video deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting video:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete video',
      message: error.message
    });
  }
});

// Delete video by ID
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const video = await Video.findById(id);

    if (!video) {
      return res.status(404).json({
        success: false,
        error: 'Video not found'
      });
    }

    // Delete the video and its files
    await video.delete();

    res.json({
      success: true,
      message: 'Video and associated files deleted successfully',
      data: {
        id: parseInt(id),
        youtube_id: video.youtube_id,
        title: video.title
      }
    });
  } catch (error) {
    console.error('Error deleting video:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete video',
      message: error.message
    });
  }
});

// Manual cleanup endpoint for maintenance
router.post('/cleanup', async (req, res) => {
  try {
    const { videoId, cleanupAll } = req.body;

    if (cleanupAll) {
      // Clean up all completed videos
      const completedVideos = await Video.findAll({
        where: { status: 'completed' }
      });

      let cleanedCount = 0;
      let errorCount = 0;

      for (const video of completedVideos) {
        try {
          await video.cleanupFiles();
          cleanedCount++;
        } catch (error) {
          console.error(`Failed to cleanup video ${video.youtube_id}:`, error.message);
          errorCount++;
        }
      }

      res.json({
        success: true,
        message: `Cleanup completed: ${cleanedCount} videos cleaned, ${errorCount} errors`,
        data: {
          cleaned: cleanedCount,
          errors: errorCount,
          total: completedVideos.length
        }
      });

    } else if (videoId) {
      // Clean up specific video
      const video = await Video.findByYouTubeId(videoId);
      if (!video) {
        return res.status(404).json({
          success: false,
          error: 'Video not found'
        });
      }

      await video.cleanupFiles();

      res.json({
        success: true,
        message: `Files cleaned up for video: ${video.title}`,
        data: { videoId: video.youtube_id }
      });

    } else {
      res.status(400).json({
        success: false,
        error: 'Please specify either videoId or set cleanupAll to true'
      });
    }

  } catch (error) {
    console.error('Cleanup operation failed:', error);
    res.status(500).json({
      success: false,
      error: 'Cleanup operation failed',
      message: error.message
    });
  }
});

module.exports = router;
