// Processing and Cleanup Configuration
// This file controls the processing behavior, validation, and cleanup for video and audio files

module.exports = {
  // Enable automatic cleanup of video and audio files after processing
  // Set to false if you want to keep the downloaded files
  enableAutoCleanup: true,

  // Cleanup on successful processing completion
  cleanupOnSuccess: true,

  // Cleanup on processing failure (recommended to prevent accumulation of failed downloads)
  cleanupOnFailure: true,

  // Log cleanup operations
  logCleanupOperations: true,

  // File validation settings
  validation: {
    // Enable strict file validation before processing
    enableStrictValidation: true,

    // Minimum file sizes (in bytes)
    minVideoSize: 100000, // 100KB
    minAudioSize: 1000,   // 1KB

    // Maximum retry attempts for failed audio extraction
    maxRetries: 2,

    // Retry delay multiplier (seconds)
    retryDelayMultiplier: 2
  }
};
